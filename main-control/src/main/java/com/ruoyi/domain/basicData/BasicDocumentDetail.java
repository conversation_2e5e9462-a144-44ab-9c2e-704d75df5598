package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 *单据明细详情表
 */
@Data
@TableName("basic_document_detail")
public class BasicDocumentDetail {

    /**
     * 主键编码
     */
    private String id;
    /**
     * 父表单据编号
     */
    private String documentCode;

    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 计划数量（单据要求的目标数量）
     * 入库场景：采购数量、生产入库计划数量
     * 出库场景：销售出库数量、生产领料数量
     */
    private Integer quantity;

    /**
     * 已完成数量（最终完成处理的数量）
     * 入库场景：已入库数量（质检合格并完成入库的数量）
     * 出库场景：已出库数量（实际已出库的数量）
     */
    private Integer completedNum;

    /**
     * 本次数量（已废弃字段，请使用对应VO类中的数量字段）
     * 单据出入库场景：使用 DocumentDetailVo.currentNum
     * 物料到货确认场景：使用 MaterialArrivalDto.arrivalQuantity
     * @deprecated 此字段已无实际用途
     */
//    @Deprecated
//    private Integer currentNum;

    /**
     * 总到货数量（累计所有批次的实际到货数量，包含不合格品）
     * 入库场景：总来料数量（所有批次累计到货，可能超过计划数量）
     * 出库场景：总准备出库数量（所有批次累计准备出库的数量）
     * 注：此数量可能大于等于已完成数量，因为包含不合格或未通过处理的部分
     */
    private Integer totalArrivalNum;
    /**
     * 任务状态：
     * 0-待处理（单据锁定后的初始状态，入库为待入库，出库为待出库）
     * 1-处理中（部分数量已处理，入库为部分入库，出库为部分出库）
     * 2-已完成（全部数量已处理完成，入库为入库完成，出库为出库完成）
     */
    private Integer taskStatus;



    /**
     * 最新批次质检状态：
     * 0-无需质检（物料本身不需要质检）
     * 1-待质检（需要质检，等待开始质检）
     * 2-质检中（质检任务进行中）
     * 3-质检合格（质检通过，可进入下一环节）
     * 4-质检不合格（质检失败，需要处理）
     * 5-免检（有质检任务但质检结果为免检）
     * 6-让步接收（质检不合格但经评估后决定接收使用）
     */
    private Integer latestQcStatus;

    /**
     * 最新批次出入库状态：
     * 0-待确认（初始状态：等待确认备货/来料）
     * 1-待出入库（有确认数量：入库为待入库，出库为待出库，支持分批）
     * 2-出入库中（正在进行出入库操作：入库为入库中，出库为出库中）
     * 3-待生产确认（仅生产相关出库单据：SCLL、SCBL、SCRK、SCTL）
     * 4-生产已确认（生产确认完成）
     * 5-待仓库确认（等待仓库最终确认）
     * 6-仓库已确认（仓库确认完成）
     * 7-已完成（最终完成状态）
     */
    private Integer latestWarehouseStatus;
}